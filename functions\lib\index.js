"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeApi = exports.essentialWebhook = exports.refundTransaction = exports.testMinimalRefund = void 0;
// Minimal functions index with refund functionality
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
admin.initializeApp();
console.log('🚀 Minimal Firebase Functions with Refund loading...');
// Test function
exports.testMinimalRefund = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Minimal refund functions working',
        timestamp: new Date().toISOString()
    });
});
// Refund transaction function - simplified version without Stripe for now
exports.refundTransaction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { orderId, amount, reason } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order details
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // For now, just update the order status without Stripe processing
        // TODO: Add Stripe refund processing later
        await orderRef.update({
            status: 'refunded',
            refundAmount: amount || (orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount) || 0,
            refundReason: reason || 'Admin refund',
            refundedAt: admin.firestore.Timestamp.now(),
            refundedBy: context.auth.uid,
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing back to active if needed
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_b = listingDoc.data()) === null || _b === void 0 ? void 0 : _b.status) === 'sold') {
                await listingRef.update({
                    status: 'active',
                    updatedAt: admin.firestore.Timestamp.now()
                });
            }
        }
        console.log(`✅ Refund processed for order: ${orderId}`);
        return {
            success: true,
            message: 'Refund processed successfully (Stripe processing will be added later)',
            orderId: orderId,
            refundAmount: amount || (orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount) || 0
        };
    }
    catch (error) {
        console.error('Error processing refund:', error);
        throw error;
    }
});
// Essential webhook for payment processing
exports.essentialWebhook = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    try {
        console.log('🔗 Essential webhook received');
        if (req.method !== 'POST') {
            res.status(405).send('Method not allowed');
            return;
        }
        // For now, just acknowledge the webhook
        // The main webhook processing is handled by the main index.ts
        res.status(200).json({ received: true, source: 'minimal-refund' });
    }
    catch (error) {
        console.error('❌ Webhook error:', error);
        res.status(500).send('Webhook failed');
    }
});
// Stripe API for checkout sessions
exports.stripeApi = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.status(200).send();
        return;
    }
    try {
        const path = req.path;
        if (path === '/create-checkout-session' && req.method === 'POST') {
            // For now, return a simple response
            // The main checkout processing is handled by the main index.ts
            res.status(200).json({
                success: false,
                error: 'Please use the main checkout endpoint',
                source: 'minimal-refund'
            });
        }
        else {
            res.status(404).json({ error: 'Endpoint not found' });
        }
    }
    catch (error) {
        console.error('Error in stripeApi:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
