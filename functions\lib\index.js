"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.createStripeConnectAccount = exports.getStripeConnectOnboardingLink = exports.stripeApi = exports.stripeWebhook = exports.refundTransaction = exports.testMinimalRefund = void 0;
// Minimal functions index with refund functionality
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const stripe_1 = __importDefault(require("stripe"));
// Initialize Firebase Admin
admin.initializeApp();
// Initialize Stripe
const stripe = new stripe_1.default(((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.STRIPE_API_KEY || '', {
    apiVersion: '2025-05-28.basil',
});
console.log('🚀 Minimal Firebase Functions with Refund loading...');
// Test function
exports.testMinimalRefund = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Minimal refund functions working',
        timestamp: new Date().toISOString()
    });
});
// Refund transaction function - simplified version without Stripe for now
exports.refundTransaction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { orderId, amount, reason } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order details
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // For now, just update the order status without Stripe processing
        // TODO: Add Stripe refund processing later
        await orderRef.update({
            status: 'refunded',
            refundAmount: amount || (orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount) || 0,
            refundReason: reason || 'Admin refund',
            refundedAt: admin.firestore.Timestamp.now(),
            refundedBy: context.auth.uid,
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing back to active if needed
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_b = listingDoc.data()) === null || _b === void 0 ? void 0 : _b.status) === 'sold') {
                await listingRef.update({
                    status: 'active',
                    updatedAt: admin.firestore.Timestamp.now()
                });
            }
        }
        console.log(`✅ Refund processed for order: ${orderId}`);
        return {
            success: true,
            message: 'Refund processed successfully (Stripe processing will be added later)',
            orderId: orderId,
            refundAmount: amount || (orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount) || 0
        };
    }
    catch (error) {
        console.error('Error processing refund:', error);
        throw error;
    }
});
// Stripe webhook for payment processing
exports.stripeWebhook = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    var _a, _b;
    try {
        console.log('🔗 Stripe webhook received');
        if (req.method !== 'POST') {
            res.status(405).send('Method not allowed');
            return;
        }
        const sig = req.headers['stripe-signature'];
        const webhookSecret = 'whsec_rEz20Kue3bkGHfmnaZilP01s614ULBQb'; // Your webhook secret
        if (!sig) {
            console.error('No Stripe signature found');
            res.status(400).send('No Stripe signature');
            return;
        }
        let event;
        try {
            event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
        }
        catch (err) {
            console.error('Webhook signature verification failed:', err.message);
            res.status(400).send(`Webhook Error: ${err.message}`);
            return;
        }
        console.log(`🎯 Processing webhook event: ${event.type}`);
        // Handle the event
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object;
                console.log('💳 Payment succeeded for session:', session.id);
                // Update order status
                const orderId = (_a = session.metadata) === null || _a === void 0 ? void 0 : _a.orderId;
                if (orderId) {
                    await admin.firestore().collection('orders').doc(orderId).update({
                        status: 'paid',
                        stripePaymentIntentId: session.payment_intent,
                        paidAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    // Update listing status to sold
                    const listingId = (_b = session.metadata) === null || _b === void 0 ? void 0 : _b.listingId;
                    if (listingId) {
                        await admin.firestore().collection('listings').doc(listingId).update({
                            status: 'sold',
                            soldAt: admin.firestore.Timestamp.now(),
                            updatedAt: admin.firestore.Timestamp.now()
                        });
                    }
                    console.log(`✅ Order ${orderId} marked as paid`);
                }
                break;
            case 'payment_intent.payment_failed':
                const paymentIntent = event.data.object;
                console.log('❌ Payment failed for payment intent:', paymentIntent.id);
                break;
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('❌ Webhook error:', error);
        res.status(500).send('Webhook failed');
    }
});
// Stripe API for checkout sessions
exports.stripeApi = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    // Enable CORS for all origins
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.set('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
        res.status(200).send();
        return;
    }
    try {
        const path = req.path;
        console.log(`🔗 StripeApi request: ${req.method} ${path}`);
        if (path === '/create-checkout-session' && req.method === 'POST') {
            // Handle checkout session creation
            try {
                // Check authorization
                const authHeader = req.headers.authorization;
                if (!authHeader || !authHeader.startsWith('Bearer ')) {
                    res.status(401).json({ error: 'Unauthorized - Missing or invalid authorization header' });
                    return;
                }
                const token = authHeader.split('Bearer ')[1];
                const decodedToken = await admin.auth().verifyIdToken(token);
                const buyerId = decodedToken.uid;
                // Get request data
                const { listingId, useWalletBalance = false, orderDetails } = req.body;
                if (!listingId) {
                    res.status(400).json({ error: 'Listing ID is required' });
                    return;
                }
                console.log(`🛒 Creating checkout for listing: ${listingId}, buyer: ${buyerId}`);
                // Get listing details
                const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
                if (!listingDoc.exists) {
                    res.status(404).json({ error: 'Listing not found' });
                    return;
                }
                const listing = listingDoc.data();
                if (!listing) {
                    res.status(404).json({ error: 'Listing data not found' });
                    return;
                }
                // Calculate amounts
                const itemPrice = listing.price;
                const shippingCost = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingFee) || 0;
                const walletBalanceUsed = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.appliedWalletCredit) || 0;
                const totalBeforeWallet = itemPrice + shippingCost;
                const finalAmount = Math.max(0, totalBeforeWallet - walletBalanceUsed);
                console.log(`💰 Pricing: Item: $${itemPrice}, Shipping: $${shippingCost}, Wallet: $${walletBalanceUsed}, Final: $${finalAmount}`);
                // Create order document
                const orderId = admin.firestore().collection('orders').doc().id;
                const orderData = {
                    id: orderId,
                    listingId,
                    buyerId,
                    sellerId: listing.ownerId,
                    status: 'pending',
                    totalAmount: finalAmount,
                    itemPrice,
                    shippingCost,
                    walletBalanceUsed,
                    originalTotal: totalBeforeWallet,
                    createdAt: admin.firestore.Timestamp.now(),
                    updatedAt: admin.firestore.Timestamp.now(),
                    deliveryMethod: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.deliveryMethod) || listing.deliveryMethod || 'in_person',
                    shippingAddress: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingAddress) || null
                };
                await admin.firestore().collection('orders').doc(orderId).set(orderData);
                // If final amount is 0 (fully paid with wallet), mark as paid
                if (finalAmount <= 0) {
                    await admin.firestore().collection('orders').doc(orderId).update({
                        status: 'paid',
                        paymentMethod: 'wallet',
                        paidAt: admin.firestore.Timestamp.now()
                    });
                    res.status(200).json({
                        success: true,
                        sessionId: null,
                        sessionUrl: null,
                        orderId,
                        message: 'Order paid with wallet balance',
                        finalAmount: 0
                    });
                    return;
                }
                // Create Stripe checkout session
                const session = await stripe.checkout.sessions.create({
                    payment_method_types: ['card'],
                    line_items: [{
                            price_data: {
                                currency: 'usd',
                                product_data: {
                                    name: `${listing.title}${walletBalanceUsed > 0 ? ` (after $${walletBalanceUsed} wallet credit)` : ''}`,
                                    description: listing.description || 'Hive Campus item',
                                },
                                unit_amount: Math.round(finalAmount * 100), // Convert to cents
                            },
                            quantity: 1,
                        }],
                    mode: 'payment',
                    success_url: `http://localhost:5174/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
                    cancel_url: `http://localhost:5174/listing/${listingId}`,
                    metadata: {
                        orderId,
                        listingId,
                        buyerId,
                        sellerId: listing.ownerId,
                        walletAmountUsed: walletBalanceUsed.toString(),
                        originalTotal: totalBeforeWallet.toString(),
                        finalAmount: finalAmount.toString(),
                    },
                });
                // Update order with session ID
                await admin.firestore().collection('orders').doc(orderId).update({
                    stripeSessionId: session.id,
                    updatedAt: admin.firestore.Timestamp.now()
                });
                console.log(`✅ Checkout session created: ${session.id}`);
                res.status(200).json({
                    success: true,
                    sessionId: session.id,
                    sessionUrl: session.url,
                    orderId,
                    finalAmount
                });
            }
            catch (checkoutError) {
                console.error('❌ Checkout session error:', checkoutError);
                res.status(500).json({
                    error: 'Failed to create checkout session',
                    details: checkoutError.message
                });
            }
        }
        else {
            res.status(404).json({ error: 'Endpoint not found' });
        }
    }
    catch (error) {
        console.error('❌ Error in stripeApi:', error);
        res.status(500).json({ error: 'Internal server error', details: error.message });
    }
});
// Get Stripe Connect onboarding link
exports.getStripeConnectOnboardingLink = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { refreshUrl, returnUrl } = data;
        const userId = context.auth.uid;
        console.log(`🔗 Getting onboarding link for user: ${userId}`);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'No Stripe Connect account found. Please create an account first.');
        }
        const connectAccount = connectAccountDoc.data();
        const stripeAccountId = connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.stripeAccountId;
        if (!stripeAccountId) {
            throw new functions.https.HttpsError('not-found', 'No Stripe account ID found');
        }
        // If already onboarded, return dashboard URL instead
        if (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) {
            return {
                onboardingUrl: connectAccount.dashboardUrl || `https://dashboard.stripe.com/connect/accounts/${stripeAccountId}`,
                isOnboarded: true
            };
        }
        // Create fresh onboarding link
        const baseUrl = 'http://localhost:5174'; // Use current dev server URL
        const accountLink = await stripe.accountLinks.create({
            account: stripeAccountId,
            refresh_url: refreshUrl || `${baseUrl}/profile?stripe_refresh=true`,
            return_url: returnUrl || `${baseUrl}/profile?stripe_success=true`,
            type: 'account_onboarding',
        });
        // Update Firestore with new onboarding URL
        await admin.firestore().collection('connectAccounts').doc(userId).update({
            onboardingUrl: accountLink.url,
            updatedAt: admin.firestore.Timestamp.now(),
        });
        console.log(`✅ Created onboarding link: ${accountLink.url}`);
        return {
            onboardingUrl: accountLink.url,
            isOnboarded: false
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectOnboardingLink:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Create Stripe Connect account
exports.createStripeConnectAccount = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { accountType } = data;
        const userId = context.auth.uid;
        if (!accountType || !['student', 'merchant'].includes(accountType)) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid account type is required');
        }
        console.log(`🏦 Creating Stripe Connect account for user: ${userId}, type: ${accountType}`);
        // Check if user already has a Connect account
        const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (existingAccountDoc.exists) {
            const existingAccount = existingAccountDoc.data();
            // If account exists but not onboarded, return existing onboarding URL
            if (!(existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.isOnboarded) && (existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.onboardingUrl)) {
                return {
                    accountId: existingAccount.stripeAccountId,
                    onboardingUrl: existingAccount.onboardingUrl,
                    message: 'Using existing account'
                };
            }
            // If already onboarded, return success
            if (existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.isOnboarded) {
                throw new functions.https.HttpsError('already-exists', 'User already has a fully onboarded Connect account');
            }
        }
        // Get user data
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const user = userDoc.data();
        // Create a Stripe Connect Express account
        const account = await stripe.accounts.create({
            type: 'express',
            country: 'US',
            email: user === null || user === void 0 ? void 0 : user.email,
            capabilities: {
                card_payments: { requested: true },
                transfers: { requested: true },
            },
            business_type: accountType === 'merchant' ? 'company' : 'individual',
            metadata: {
                userId,
                accountType
            }
        });
        // Create an account link for onboarding
        const baseUrl = 'http://localhost:5174'; // Use current dev server URL
        const accountLink = await stripe.accountLinks.create({
            account: account.id,
            refresh_url: `${baseUrl}/profile?stripe_refresh=true`,
            return_url: `${baseUrl}/profile?stripe_success=true`,
            type: 'account_onboarding',
        });
        // Store the Connect account in Firestore
        await admin.firestore().collection('connectAccounts').doc(userId).set({
            userId: userId,
            stripeAccountId: account.id,
            accountType: accountType,
            isOnboarded: false,
            chargesEnabled: false,
            payoutsEnabled: false,
            detailsSubmitted: false,
            onboardingUrl: accountLink.url,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
        });
        console.log(`✅ Created Stripe Connect account ${account.id} for user ${userId}`);
        return {
            accountId: account.id,
            onboardingUrl: accountLink.url,
            message: 'Connect account created successfully'
        };
    }
    catch (error) {
        console.error('Error in createStripeConnectAccount:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
